import * as React from 'react';
import * as <PERSON><PERSON>D<PERSON> from 'react-dom';
import * as ReactJsxDevRuntime from 'react/jsx-dev-runtime';
import * as ReactJsxRuntime from 'react/jsx-runtime';
import * as ReactCompilerRuntime from 'react/compiler-runtime';
import * as ReactDOMServerEdge from 'react-dom/server.edge';
declare let ReactServerDOMTurbopackClientEdge: any, ReactServerDOMWebpackClientEdge: any;
export { React, ReactJsxDevRuntime, ReactJsxRuntime, ReactCompilerRuntime, ReactDOM, ReactDOMServerEdge, ReactServerDOMTurbopackClientEdge, ReactServerDOMWebpackClientEdge, };
